<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="346a0bff-376a-4cdb-9020-98a3c945212f" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="HTML File" />
        <option value="JavaScript File" />
        <option value="CSS File" />
        <option value="Python Script" />
      </list>
    </option>
  </component>
  <component name="FlaskConsoleOptions" custom-start-script="import sys&#10;sys.path.extend([WORKING_DIR_AND_PYTHON_PATHS])&#10;from flask.cli import ScriptInfo&#10;locals().update(ScriptInfo(create_app=None).load_app().make_shell_context())&#10;print(&quot;Python %s on %s\nApp: %s [%s]\nInstance: %s&quot; % (sys.version, sys.platform, app.import_name, app.env, app.instance_path))">
    <envs>
      <env key="FLASK_APP" value="app" />
    </envs>
    <option name="myCustomStartScript" value="import sys&#10;sys.path.extend([WORKING_DIR_AND_PYTHON_PATHS])&#10;from flask.cli import ScriptInfo&#10;locals().update(ScriptInfo(create_app=None).load_app().make_shell_context())&#10;print(&quot;Python %s on %s\nApp: %s [%s]\nInstance: %s&quot; % (sys.version, sys.platform, app.import_name, app.env, app.instance_path))" />
    <option name="myEnvs">
      <map>
        <entry key="FLASK_APP" value="app" />
      </map>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectId" id="2yiY3nbEEwQF0Qiat0PhnV9PxIW" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;DefaultHtmlFileTemplate&quot;: &quot;HTML File&quot;,
    &quot;JavaScript 调试.admin.html.executor&quot;: &quot;Run&quot;,
    &quot;JavaScript 调试.student.html.executor&quot;: &quot;Run&quot;,
    &quot;JavaScript 调试.teacher_analyze.html.executor&quot;: &quot;Run&quot;,
    &quot;JavaScript 调试.teacher_manage.html.executor&quot;: &quot;Run&quot;,
    &quot;Node.js.log.js.executor&quot;: &quot;Run&quot;,
    &quot;Node.js.teacher_analyze.js.executor&quot;: &quot;Run&quot;,
    &quot;Python.MySQL.executor&quot;: &quot;Run&quot;,
    &quot;Python.app.executor&quot;: &quot;Run&quot;,
    &quot;Python.config.executor&quot;: &quot;Run&quot;,
    &quot;Python.database_operations.executor&quot;: &quot;Run&quot;,
    &quot;Python.excel_mysql.executor&quot;: &quot;Run&quot;,
    &quot;Python.excel_mysql_account.executor&quot;: &quot;Run&quot;,
    &quot;Python.excel_to_mysql.executor&quot;: &quot;Run&quot;,
    &quot;Python.mysql_complete.executor&quot;: &quot;Run&quot;,
    &quot;Python.mysql_python.executor&quot;: &quot;Run&quot;,
    &quot;Python.mysql_python_account.executor&quot;: &quot;Run&quot;,
    &quot;Python.test_api.executor&quot;: &quot;Run&quot;,
    &quot;Python.test_delete_charts.executor&quot;: &quot;Run&quot;,
    &quot;Python.test_teacher_api.executor&quot;: &quot;Run&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/学生成绩App&quot;,
    &quot;list.type.of.created.stylesheet&quot;: &quot;CSS&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;DatabaseDriversLRU&quot;: [
      &quot;mysql_aurora_aws&quot;,
      &quot;mysql&quot;
    ]
  }
}</component>
  <component name="RunManager" selected="Python.app">
    <configuration name="MySQL_to_pythonr" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="学生成绩app" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/backend" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="D:\学生成绩App\backend\MySQL_to_pythonr.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="app" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="学生成绩app" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/app.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="excel_mysql" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="学生成绩app" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/backend" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/backend/excel_mysql.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="excel_mysql_account" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="学生成绩app" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/backend" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/backend/excel_mysql_account.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="excel_to_mysql" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="学生成绩app" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/backend" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/backend/excel_to_mysql.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Python.app" />
        <item itemvalue="Python.MySQL_to_pythonr" />
        <item itemvalue="Python.excel_to_mysql" />
        <item itemvalue="Python.excel_mysql_account" />
        <item itemvalue="Python.excel_mysql" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-1d06a55b98c1-0b3e54e931b4-JavaScript-PY-241.19416.19" />
        <option value="bundled-python-sdk-337b0a7a993a-2767605e8bc2-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-241.19416.19" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="346a0bff-376a-4cdb-9020-98a3c945212f" name="更改" comment="" />
      <created>*************</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>*************</updated>
      <workItem from="*********4277" duration="227000" />
      <workItem from="1750345853041" duration="271000" />
      <workItem from="1750346389494" duration="347000" />
      <workItem from="1750399944053" duration="160000" />
      <workItem from="1750489258410" duration="12000" />
      <workItem from="1750661854705" duration="1304000" />
      <workItem from="1750664020622" duration="247000" />
      <workItem from="1750664740299" duration="1592000" />
      <workItem from="1750670001895" duration="442000" />
      <workItem from="1750681188036" duration="2092000" />
      <workItem from="1750683313709" duration="848000" />
      <workItem from="1750684171423" duration="4848000" />
      <workItem from="1750690719341" duration="1742000" />
      <workItem from="1750726410327" duration="2298000" />
      <workItem from="1750730872885" duration="755000" />
      <workItem from="1750731838169" duration="1049000" />
      <workItem from="1750732912752" duration="1553000" />
      <workItem from="1750745396920" duration="5461000" />
      <workItem from="1750763982238" duration="1950000" />
      <workItem from="1750767745223" duration="1541000" />
      <workItem from="1750771289710" duration="1130000" />
      <workItem from="1750772434428" duration="1241000" />
      <workItem from="1750774094825" duration="4123000" />
      <workItem from="1750778292594" duration="104000" />
      <workItem from="1750778504383" duration="97000" />
      <workItem from="1750778675000" duration="951000" />
      <workItem from="1750811754885" duration="9482000" />
      <workItem from="1750833167908" duration="2517000" />
      <workItem from="1750860711656" duration="4225000" />
      <workItem from="1750864975378" duration="2355000" />
      <workItem from="1750897952664" duration="870000" />
      <workItem from="1750900671188" duration="5002000" />
      <workItem from="1750917671258" duration="1614000" />
      <workItem from="1750920612119" duration="2436000" />
      <workItem from="1750923102952" duration="9000" />
      <workItem from="1750926761266" duration="1585000" />
      <workItem from="1750928524731" duration="174000" />
      <workItem from="1750936315092" duration="11976000" />
      <workItem from="1750954354534" duration="4467000" />
      <workItem from="1750988034200" duration="82000" />
      <workItem from="1750988117419" duration="1355000" />
      <workItem from="1750991571692" duration="634000" />
      <workItem from="1751009766559" duration="3556000" />
      <workItem from="1751093397815" duration="16000" />
      <workItem from="1751115755485" duration="37000" />
      <workItem from="1751118378573" duration="3000" />
      <workItem from="1751188920341" duration="2664000" />
      <workItem from="1751200033178" duration="113000" />
      <workItem from="1751200207288" duration="1570000" />
      <workItem from="1751202038797" duration="971000" />
      <workItem from="1751207976120" duration="1825000" />
      <workItem from="1751244123537" duration="4931000" />
      <workItem from="1751249461012" duration="1642000" />
      <workItem from="1751251613422" duration="1533000" />
      <workItem from="1751253344362" duration="593000" />
      <workItem from="1751254109636" duration="63000" />
      <workItem from="1751263467167" duration="8637000" />
      <workItem from="1751284631573" duration="4360000" />
      <workItem from="1751292474374" duration="1709000" />
      <workItem from="1751330581306" duration="3989000" />
      <workItem from="1751340082129" duration="144000" />
      <workItem from="1751349319640" duration="1346000" />
      <workItem from="1751352665391" duration="4301000" />
      <workItem from="1751359034316" duration="1313000" />
      <workItem from="1751365779216" duration="3716000" />
      <workItem from="1751417643549" duration="1756000" />
      <workItem from="1751434839133" duration="4510000" />
      <workItem from="1751445482011" duration="694000" />
      <workItem from="1751447808060" duration="491000" />
      <workItem from="1751456860162" duration="3166000" />
      <workItem from="1751460063133" duration="491000" />
      <workItem from="1751503814845" duration="3413000" />
      <workItem from="1751552997604" duration="72000" />
      <workItem from="1751589537816" duration="2733000" />
      <workItem from="1751595712360" duration="186000" />
      <workItem from="1751598052799" duration="353000" />
      <workItem from="1751610490539" duration="4707000" />
      <workItem from="1751619757864" duration="869000" />
      <workItem from="1751696290413" duration="4675000" />
      <workItem from="1751709616259" duration="126000" />
      <workItem from="1751721083590" duration="2041000" />
      <workItem from="1751727909007" duration="754000" />
      <workItem from="1751728687714" duration="4713000" />
      <workItem from="1751734447176" duration="2862000" />
      <workItem from="1751737994520" duration="315000" />
      <workItem from="1751786661323" duration="3648000" />
      <workItem from="1751848797980" duration="9139000" />
      <workItem from="1751868024617" duration="1825000" />
      <workItem from="1751877124098" duration="3237000" />
      <workItem from="1751885208010" duration="5445000" />
      <workItem from="1751935437506" duration="5923000" />
      <workItem from="1751955008096" duration="1655000" />
      <workItem from="1751958672440" duration="54000" />
      <workItem from="1751966603930" duration="125000" />
      <workItem from="1752021096441" duration="486000" />
      <workItem from="1752021715164" duration="1236000" />
      <workItem from="1752064717737" duration="266000" />
      <workItem from="1752065021145" duration="405000" />
      <workItem from="1752110136451" duration="1981000" />
      <workItem from="1752137733831" duration="245000" />
      <workItem from="1752139350799" duration="180000" />
      <workItem from="1752140470443" duration="406000" />
      <workItem from="1752195002111" duration="11000" />
      <workItem from="1752197032650" duration="33000" />
      <workItem from="1752221557945" duration="495000" />
      <workItem from="1752222079327" duration="64000" />
      <workItem from="1752223714961" duration="9000" />
      <workItem from="1752224089686" duration="185000" />
      <workItem from="1752236049041" duration="1648000" />
      <workItem from="1752248179607" duration="6000" />
      <workItem from="1752248205620" duration="388000" />
      <workItem from="1752248612157" duration="3000" />
      <workItem from="1752248627450" duration="13000" />
      <workItem from="1752250353268" duration="361000" />
      <workItem from="1752305896304" duration="2124000" />
      <workItem from="1752321537335" duration="1675000" />
      <workItem from="1752404121371" duration="181000" />
      <workItem from="1752407482319" duration="29000" />
      <workItem from="1752416201359" duration="752000" />
      <workItem from="1752503244463" duration="73000" />
      <workItem from="1752541346086" duration="7000" />
      <workItem from="1752549050043" duration="1182000" />
      <workItem from="1752559991502" duration="1868000" />
      <workItem from="1752564565577" duration="78000" />
      <workItem from="1752565269879" duration="1190000" />
      <workItem from="1752712685039" duration="12000" />
      <workItem from="1752741186939" duration="186000" />
      <workItem from="1753019236697" duration="866000" />
      <workItem from="1753022100282" duration="203000" />
      <workItem from="1753104342118" duration="1250000" />
      <workItem from="1753105944939" duration="2922000" />
      <workItem from="1753182111936" duration="879000" />
      <workItem from="1753184350106" duration="440000" />
      <workItem from="1753185025116" duration="315000" />
      <workItem from="1753185361217" duration="63000" />
      <workItem from="1753185723514" duration="1802000" />
      <workItem from="1753188383113" duration="1262000" />
      <workItem from="1753192247858" duration="3891000" />
      <workItem from="1753253982442" duration="308000" />
      <workItem from="1753259439046" duration="4940000" />
      <workItem from="1753266158414" duration="6095000" />
      <workItem from="*************" duration="1938000" />
      <workItem from="*************" duration="3154000" />
      <workItem from="*************" duration="990000" />
      <workItem from="*************" duration="1565000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/app$excel_mysql_account.coverage" NAME="excel_mysql_account 覆盖结果" MODIFIED="*************" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/backend" />
    <SUITE FILE_PATH="coverage/app_py$app.coverage" NAME="app 覆盖结果" MODIFIED="*************" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/app_py$mysql_complete.coverage" NAME="mysql_complete 覆盖结果" MODIFIED="*************" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/MySQL" />
    <SUITE FILE_PATH="coverage/app___$app.coverage" NAME="app 覆盖结果" MODIFIED="*************" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/app$app.coverage" NAME="app 覆盖结果" MODIFIED="1750866728266" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/app_py$MySQL.coverage" NAME="MySQL 覆盖结果" MODIFIED="1751209127265" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/backend" />
    <SUITE FILE_PATH="coverage/App$excel_to_mysql.coverage" NAME="excel_to_mysql 覆盖结果" MODIFIED="*************" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/backend" />
    <SUITE FILE_PATH="coverage/app$mysql_python_account.coverage" NAME="mysql_python_account 覆盖结果" MODIFIED="*************" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/backend" />
    <SUITE FILE_PATH="coverage/app_py$test_api.coverage" NAME="test_api 覆盖结果" MODIFIED="*************" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/app_py$test_teacher_api.coverage" NAME="test_teacher_api 覆盖结果" MODIFIED="*************" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/app_py$excel_mysql_account.coverage" NAME="excel_mysql_account 覆盖结果" MODIFIED="*************" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/backend" />
    <SUITE FILE_PATH="coverage/app$excel_mysql.coverage" NAME="excel_mysql 覆盖结果" MODIFIED="*************" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/backend" />
    <SUITE FILE_PATH="coverage/app$mysql_python.coverage" NAME="mysql_python 覆盖结果" MODIFIED="*************" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/backend" />
    <SUITE FILE_PATH="coverage/app$config.coverage" NAME="config 覆盖结果" MODIFIED="*************" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/backend" />
    <SUITE FILE_PATH="coverage/App$excel_mysql_account.coverage" NAME="excel_mysql_account 覆盖结果" MODIFIED="*************" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/backend" />
    <SUITE FILE_PATH="coverage/App$app.coverage" NAME="app 覆盖结果" MODIFIED="*************" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/app_py$test_delete_charts.coverage" NAME="test_delete_charts 覆盖结果" MODIFIED="*************" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/app_py$excel_to_mysql.coverage" NAME="excel_to_mysql 覆盖结果" MODIFIED="*************" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/backend" />
    <SUITE FILE_PATH="coverage/App$excel_mysql.coverage" NAME="excel_mysql 覆盖结果" MODIFIED="1753186092886" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/backend" />
    <SUITE FILE_PATH="coverage/app_py$excel_mysql.coverage" NAME="excel_mysql 覆盖结果" MODIFIED="1752567792447" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/backend" />
    <SUITE FILE_PATH="coverage/App$database_operations.coverage" NAME="database_operations 覆盖结果" MODIFIED="1753281538456" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/backend" />
  </component>
</project>